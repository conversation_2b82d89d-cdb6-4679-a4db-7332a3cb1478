<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI代码审核反馈</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .gitlab-panel {
            width: 80%;
            height: 100%;
            border-right: 2px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .gitlab-placeholder {
            text-align: center;
            color: white;
            padding: 40px;
        }

        .gitlab-placeholder h2 {
            font-size: 28px;
            margin-bottom: 15px;
            font-weight: 300;
        }

        .gitlab-placeholder p {
            font-size: 16px;
            margin-bottom: 25px;
            opacity: 0.9;
            line-height: 1.5;
        }

        .open-gitlab-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .open-gitlab-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .feedback-panel {
            width: 20%;
            height: 100%;
            background: white;
            display: flex;
            flex-direction: column;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .feedback-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .feedback-header h2 {
            font-size: 18px;
            margin-bottom: 5px;
        }

        .feedback-header p {
            font-size: 12px;
            opacity: 0.9;
        }

        .feedback-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            display: block;
        }

        .radio-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .radio-item {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background-color 0.3s;
            border: 1px solid #e9ecef;
        }

        .radio-item:hover {
            background-color: #f8f9fa;
        }

        .radio-item.selected {
            background-color: #e3f2fd;
            border-color: #4facfe;
        }

        .radio-item input[type="radio"] {
            margin-right: 8px;
        }

        .radio-item span {
            font-size: 13px;
        }

        .submit-section {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px;
            font-size: 14px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .submit-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 15px;
        }

        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4facfe;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin: 0 auto 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .gitlab-panel {
                width: 75%;
            }
            .feedback-panel {
                width: 25%;
            }
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            .gitlab-panel {
                width: 100%;
                height: 70%;
            }
            .feedback-panel {
                width: 100%;
                height: 30%;
            }
        }

        .feedback-result {
            text-align: center;
            padding: 40px 20px;
        }

        .result-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .feedback-result h3 {
            color: #333;
            margin-bottom: 25px;
            font-size: 20px;
        }

        .result-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: left;
        }

        .result-info p {
            margin: 8px 0;
            color: #555;
        }

        .result-note {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- GitLab页面展示区域 -->
        <div class="gitlab-panel">
            <div class="gitlab-placeholder">
                <h2>GitLab代码审核页面</h2>
                <p>由于安全限制，无法在此处直接显示GitLab页面<br>请点击下方按钮在新窗口中查看代码审核结果</p>
                <button id="openGitlabBtn" class="open-gitlab-btn">🔗 打开GitLab页面</button>
            </div>
        </div>

        <!-- 反馈表单区域 -->
        <div class="feedback-panel">
            <div class="feedback-header">
                <h2>AI代码审核反馈</h2>
                <p>请评估本次审核结果</p>
            </div>

            <div class="feedback-content">
                <form id="feedbackForm">
                    <!-- 有用性评估 -->
                    <div class="form-group">
                        <label class="form-label">本次AI代码审核评价：</label>
                        <div class="radio-group">
                            <label class="radio-item">
                                <input type="radio" name="evaluation" value="useful" required>
                                <span>有用</span>
                            </label>
                            <label class="radio-item">
                                <input type="radio" name="evaluation" value="useless" required>
                                <span>没用</span>
                            </label>
                            <label class="radio-item">
                                <input type="radio" name="evaluation" value="test" required>
                                <span>测试代码</span>
                            </label>
                        </div>
                    </div>

                    <!-- 建议输入框（仅在选择"没用"时显示） -->
                    <div class="form-group" id="suggestionGroup" style="display: none;">
                        <label class="form-label">请提供优化建议（必填）：</label>
                        <textarea
                            id="suggestionInput"
                            name="suggestion"
                            placeholder="请详细描述您认为AI审核可以改进的地方..."
                            rows="4"
                            style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 14px; resize: vertical; font-family: inherit;"
                        ></textarea>
                        <div class="suggestion-hint" style="font-size: 12px; color: #666; margin-top: 5px;">
                            例如：审核过于严格、遗漏了重要问题、建议不够具体等
                        </div>
                    </div>
                </form>
            </div>

            <div class="submit-section">
                <button type="submit" class="submit-btn" id="submitBtn" form="feedbackForm">提交反馈</button>
                
                <!-- 加载状态 -->
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p style="font-size: 12px;">正在提交...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            checkFeedbackStatus();
            initializeForm();
            loadGitlabPage();
        });

        async function checkFeedbackStatus() {
            const urlParams = new URLSearchParams(window.location.search);
            const gitlabUrl = urlParams.get('gitlab_url');
            
            if (!gitlabUrl) {
                return;
            }

            try {
                // 检查反馈状态
                const response = await fetch(`/review/feedback/status?gitlab_url=${encodeURIComponent(gitlabUrl)}`);
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.has_feedback) {
                        showFeedbackResult(result);
                        return;
                    }
                }
            } catch (error) {
                console.error('检查反馈状态失败:', error);
            }
        }

        function showFeedbackResult(result) {
            const feedbackContent = document.querySelector('.feedback-content');
            const submitSection = document.querySelector('.submit-section');
            
            // 隐藏表单和提交按钮
            feedbackContent.innerHTML = `
                <div class="feedback-result">
                    <div class="result-icon">✅</div>
                    <h3>反馈已完成</h3>
                    <div class="result-info">
                        <p><strong>评价结果：</strong>${result.evaluation_text}</p>
                        <p><strong>反馈时间：</strong>${new Date(result.updated_at).toLocaleString()}</p>
                    </div>
                    <p class="result-note">感谢您的反馈！</p>
                </div>
            `;
            
            submitSection.style.display = 'none';
        }

        function initializeForm() {
            const radioButtons = document.querySelectorAll('input[name="evaluation"]');
            const suggestionGroup = document.getElementById('suggestionGroup');
            const suggestionInput = document.getElementById('suggestionInput');

            radioButtons.forEach(radio => {
                radio.addEventListener('change', function() {
                    // 移除所有选中样式
                    document.querySelectorAll('.radio-item').forEach(item => {
                        item.classList.remove('selected');
                    });

                    // 添加选中样式
                    this.closest('.radio-item').classList.add('selected');

                    // 根据选择显示/隐藏建议输入框
                    if (this.value === 'useless') {
                        suggestionGroup.style.display = 'block';
                        suggestionInput.required = true;
                    } else {
                        suggestionGroup.style.display = 'none';
                        suggestionInput.required = false;
                        suggestionInput.value = ''; // 清空建议内容
                    }
                });
            });

            document.getElementById('feedbackForm').addEventListener('submit', handleSubmit);
        }

        function loadGitlabPage() {
            // 从URL参数获取GitLab页面地址
            const urlParams = new URLSearchParams(window.location.search);
            const gitlabUrl = urlParams.get('gitlab_url');
            
            if (gitlabUrl) {
                document.getElementById('openGitlabBtn').onclick = function() {
                    window.open(decodeURIComponent(gitlabUrl), '_blank');
                };
            }
        }

        async function handleSubmit(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const evaluation = formData.get('evaluation');
            const suggestion = formData.get('suggestion');

            if (!evaluation) {
                alert('请选择审核评价');
                return;
            }

            // 如果选择"没用"，必须填写建议
            if (evaluation === 'useless') {
                if (!suggestion || !suggestion.trim()) {
                    alert('选择"没用"时必须填写优化建议');
                    return;
                }
            }

            const urlParams = new URLSearchParams(window.location.search);
            const gitlabUrl = urlParams.get('gitlab_url');

            if (!gitlabUrl) {
                alert('缺少GitLab URL参数');
                return;
            }

            const submitData = {
                evaluation: evaluation,
                gitlabUrl: decodeURIComponent(gitlabUrl),
                timestamp: new Date().toISOString()
            };

            // 只有在选择"没用"时才添加建议
            if (evaluation === 'useless' && suggestion && suggestion.trim()) {
                submitData.suggestion = suggestion.trim();
            }

            console.log('提交数据:', submitData);
            
            // 显示加载状态
            showLoading(true);
            
            try {
                const response = await fetch('/api/review/useful', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(submitData)
                });

                const result = await response.json();
                
                if (response.ok) {
                    alert('反馈提交成功！感谢您的评价。');
                    resetForm();
                } else {
                    alert(`提交失败: ${result.error || '未知错误'}`);
                }
                
            } catch (error) {
                console.error('提交失败:', error);
                alert('网络错误，请重试');
            } finally {
                showLoading(false);
            }
        }

        function showLoading(show) {
            const loading = document.getElementById('loading');
            const submitBtn = document.getElementById('submitBtn');
            
            if (show) {
                loading.style.display = 'block';
                submitBtn.style.display = 'none';
            } else {
                loading.style.display = 'none';
                submitBtn.style.display = 'block';
            }
        }

        function resetForm() {
            document.getElementById('feedbackForm').reset();
            document.querySelectorAll('.radio-item').forEach(item => {
                item.classList.remove('selected');
            });
        }
    </script>
</body>
</html>
